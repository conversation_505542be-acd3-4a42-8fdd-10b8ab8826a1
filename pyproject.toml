[tool.ruff]
line-length = 100
target-version = "py311"

[tool.ruff.lint]
select = [
  "E",
  "F",
  "I",
  "D",
  "UP",
  "B",
  "C4",
  "C90",
  "P<PERSON>",
  "D",
  "ICN",
  "RET",
  "SIM",
  "TID",
  "PTH",
]

ignore = [
  "F841", # local variable assigned but never used
  "E302", # too many blank lines
  "E203", # whitespace before ':'
  "F821", # undefined name
  "D100", # missing-module-docstring
  "D101", # missing-class-docstring
  "D103", # missing-function-docstring
  "D212",
  "D400", # first line of the docstring must end with a period
  "D415", # first line of the docstring must end with a period, question mark, or exclamation point
  "D417",
]

unfixable = [
  "F401", # unused-import
]

[tool.ruff.lint.pylint]
max-branches = 12
max-returns = 8
max-public-methods = 25

[tool.ruff.lint.pydocstyle]
# https://github.com/google/styleguide/blob/gh-pages/pyguide.md#38-comments-and-docstrings
convention = "google"

[build-system]
requires = ["setuptools>=68", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "free_models_updater"
version = "0.1.0"
# authors = [{ name = "", email = "" }]
description = ""
readme = "README.md"
license = { text = "MIT" }
requires-python = ">=3.11"

# 從 requirements.txt 同步的實際依賴
dependencies = [
    "httpx>=0.25.0",
    "python-dotenv>=1.0.0",
    "notion-client>=2.4.0",
]

keywords = ["machine learning", "evaluation", "nlp", "transformers", "llm"]
classifiers = [
  "Development Status :: 3 - Alpha",
  "Intended Audience :: Developers",
  "Intended Audience :: Science/Research",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3 :: Only",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Operating System :: OS Independent",
  "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

# CLI 入口
[project.scripts]
free-models-updater = "free_models_updater.main:main"

# 專案連結
[project.urls]
# Source = ""
# Bug-Tracker = ""

# ---- setuptools 專用設定 ----
[tool.setuptools]
include-package-data = true

# 自動發現所有套件，類似於 setup.py 中的 find_packages()
[tool.setuptools.packages.find]
# 從這些目錄開始尋找套件
where = ["."]
# 包含這些套件名稱模式
include = ["free_models_updater*"]
# 排除這些套件（如果需要的話）
# exclude = ["tests*"]
