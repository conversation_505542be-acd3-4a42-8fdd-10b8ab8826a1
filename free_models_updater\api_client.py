"""API 客戶端模組"""

import httpx
from typing import List, Dict, Any
from .config import config
from .models import ModelInfo

class CohereAPIClient:
    """Cohere API 客戶端"""
    
    def __init__(self):
        self.base_url = config.cohere_api_endpoint
        self.api_key = config.cohere_api_key
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    async def fetch_models(self) -> List[ModelInfo]:
        """從 API 端點獲取模型資料"""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                self.base_url,
                headers=self.headers,
                timeout=30.0
            )
            response.raise_for_status()
            
            data = response.json()
            models = []
            
            for model_data in data.get("models", []):
                model = ModelInfo(
                    name=model_data.get("name", ""),
                    endpoints=model_data.get("endpoints", []),
                    context_length=model_data.get("context_length", 0),
                    supports_vision=model_data.get("supports_vision", False),
                    finetuned=model_data.get("finetuned", False),
                    tokenizer_url=model_data.get("tokenizer_url"),
                    features=model_data.get("features")
                )
                models.append(model)
            
            return models