"""Notion 客戶端模組"""

import httpx
from typing import List, Dict, Any
from .config import config
from .models import ModelInfo

class NotionClient:
    """Notion API 客戶端"""
    
    def __init__(self, database_id: str):
        self.database_id = database_id
        self.base_url = "https://api.notion.com/v1"
        self.headers = {
            "Authorization": f"Bearer {config.notion_api_key}",
            "Content-Type": "application/json",
            "Notion-Version": "2022-06-28"
        }
    
    async def create_page(self, model: ModelInfo) -> Dict[str, Any]:
        """在 Notion 資料庫中建立新頁面"""
        page_data = {
            "parent": {"database_id": self.database_id},
            "properties": {
                "名稱": {
                    "title": [{"text": {"content": model.name}}]
                },
                "類型": {
                    "select": {"name": model.model_type}
                },
                "端點": {
                    "multi_select": [{"name": endpoint} for endpoint in model.endpoints]
                },
                "上下文長度": {
                    "number": model.context_length
                },
                "視覺支援": {
                    "select": {"name": model.vision_support}
                },
                "微調模型": {
                    "select": {"name": model.fine_tuned_status}
                }
            }
        }
        
        if model.tokenizer_url:
            page_data["properties"]["Tokenizer URL"] = {
                "url": model.tokenizer_url
            }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/pages",
                headers=self.headers,
                json=page_data,
                timeout=30.0
            )
            response.raise_for_status()
            return response.json()
    
    async def update_database(self, models: List[ModelInfo]) -> List[Dict[str, Any]]:
        """批量更新 Notion 資料庫"""
        results = []
        for model in models:
            try:
                result = await self.create_page(model)
                results.append(result)
                print(f"✅ 已新增模型: {model.name}")
            except Exception as e:
                print(f"❌ 新增模型失敗 {model.name}: {e}")
        
        return results