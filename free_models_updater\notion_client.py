"""Notion 客戶端模組"""

from typing import List, Dict, Any
from notion_client import Client
from .config import config
from .models import ModelInfo

class NotionClient:
    """Notion API 客戶端"""

    def __init__(self, database_id: str):
        self.database_id = database_id
        self.client = Client(auth=config.notion_api_key)

    def test_connection(self) -> bool:
        """測試 Notion API 連接和資料庫存取權限"""
        try:
            # 測試 API 連接
            self.client.users.me()
            print("✅ Notion API 連接成功")

            # 測試資料庫存取
            database = self.client.databases.retrieve(self.database_id)
            print(f"✅ 資料庫存取成功: {database.get('title', [{}])[0].get('plain_text', 'Unknown')}")

            # 顯示資料庫屬性
            properties = database.get('properties', {})
            print(f"📋 資料庫屬性: {list(properties.keys())}")

            return True
        except Exception as e:
            print(f"❌ Notion 連接測試失敗: {e}")
            return False
    
    def create_page(self, model: ModelInfo) -> Dict[str, Any]:
        """在 Notion 資料庫中建立新頁面"""
        properties = {
            "名稱": {
                "title": [{"text": {"content": model.name}}]
            },
            "類型": {
                "select": {"name": model.model_type}
            },
            "端點": {
                "multi_select": [{"name": endpoint} for endpoint in model.endpoints]
            },
            "上下文長度": {
                "number": model.context_length
            },
            "視覺支援": {
                "select": {"name": model.vision_support}
            },
            "微調模型": {
                "select": {"name": model.fine_tuned_status}
            }
        }

        if model.tokenizer_url:
            properties["Tokenizer URL"] = {
                "url": model.tokenizer_url
            }

        return self.client.pages.create(
            parent={"database_id": self.database_id},
            properties=properties
        )
    
    def update_database(self, models: List[ModelInfo]) -> List[Dict[str, Any]]:
        """批量更新 Notion 資料庫"""
        results = []
        for model in models:
            try:
                result = self.create_page(model)
                results.append(result)
                print(f"✅ 已新增模型: {model.name}")
            except Exception as e:
                print(f"❌ 新增模型失敗 {model.name}: {e}")

        return results