"""Notion 客戶端模組"""

from typing import List, Dict, Any
from notion_client import Client
from .config import config
from .models import ModelInfo

class NotionClient:
    """Notion API 客戶端"""

    def __init__(self, database_id: str):
        self.database_id = database_id
        self.client = Client(auth=config.notion_api_key)

    def test_connection(self) -> tuple[bool, dict]:
        """測試 Notion API 連接和資料庫存取權限"""
        try:
            # 測試 API 連接
            self.client.users.me()
            print("✅ Notion API 連接成功")

            # 測試資料庫存取
            database = self.client.databases.retrieve(self.database_id)
            print(f"✅ 資料庫存取成功: {database.get('title', [{}])[0].get('plain_text', 'Unknown')}")

            # 顯示資料庫屬性
            properties = database.get('properties', {})
            print(f"📋 資料庫屬性: {list(properties.keys())}")

            return True, properties
        except Exception as e:
            print(f"❌ Notion 連接測試失敗: {e}")
            return False, {}
    
    def create_page(self, model: ModelInfo, available_properties: dict) -> Dict[str, Any]:
        """在 Notion 資料庫中建立新頁面"""
        properties = self._build_properties(model, available_properties)

        return self.client.pages.create(
            parent={"database_id": self.database_id},
            properties=properties
        )
    
    def find_existing_model(self, model_name: str, available_properties: dict) -> Dict[str, Any] | None:
        """查找資料庫中是否已存在指定名稱的模型"""
        try:
            # 確定用於查詢的標題欄位名稱
            title_field = None
            if "Model Name" in available_properties:
                title_field = "Model Name"
            elif "名稱" in available_properties:
                title_field = "名稱"
            elif "Name" in available_properties:
                title_field = "Name"

            if not title_field:
                return None

            # 查詢資料庫中是否存在同名模型
            response = self.client.databases.query(
                database_id=self.database_id,
                filter={
                    "property": title_field,
                    "title": {
                        "equals": model_name
                    }
                }
            )

            results = response.get("results", [])
            return results[0] if results else None

        except Exception as e:
            print(f"⚠️ 查詢現有模型失敗 {model_name}: {e}")
            return None

    def update_existing_page(self, page_id: str, model: ModelInfo, available_properties: dict) -> Dict[str, Any]:
        """更新現有的頁面"""
        properties = self._build_properties(model, available_properties)

        return self.client.pages.update(
            page_id=page_id,
            properties=properties
        )

    def _build_properties(self, model: ModelInfo, available_properties: dict) -> Dict[str, Any]:
        """建構屬性字典（供新增和更新使用）"""
        properties = {}

        # Model Name (Title 欄位)
        if "Model Name" in available_properties:
            properties["Model Name"] = {
                "title": [{"text": {"content": model.name}}]
            }
        elif "名稱" in available_properties:
            properties["名稱"] = {
                "title": [{"text": {"content": model.name}}]
            }
        elif "Name" in available_properties:
            properties["Name"] = {
                "title": [{"text": {"content": model.name}}]
            }

        # Context Length (Number 欄位)
        if "Context Length" in available_properties:
            properties["Context Length"] = {
                "number": model.context_length
            }
        elif "上下文長度" in available_properties:
            properties["上下文長度"] = {
                "number": model.context_length
            }

        # Maximum Output Tokens (Number 欄位)
        if "Maximum Output Tokens" in available_properties:
            properties["Maximum Output Tokens"] = {
                "number": model.get_maximum_output_tokens()
            }

        # Endpoints (Multi-select 欄位)
        if "Endpoints" in available_properties:
            properties["Endpoints"] = {
                "multi_select": [{"name": endpoint} for endpoint in model.endpoints]
            }
        elif "端點" in available_properties:
            properties["端點"] = {
                "multi_select": [{"name": endpoint} for endpoint in model.endpoints]
            }

        # Vision (Select 欄位)
        if "Vision" in available_properties:
            properties["Vision"] = {
                "select": {"name": model.vision_status}
            }
        elif "視覺支援" in available_properties:
            properties["視覺支援"] = {
                "select": {"name": model.vision_support}
            }
        elif "Vision Support" in available_properties:
            properties["Vision Support"] = {
                "select": {"name": model.vision_support}
            }

        # Rate Limit (可能是 Text 或 Select 欄位)
        if "Rate Limit" in available_properties:
            # 檢查欄位類型
            field_type = available_properties["Rate Limit"].get("type", "")
            if field_type == "select":
                properties["Rate Limit"] = {
                    "select": {"name": model.get_rate_limit()}
                }
            else:  # 假設是 rich_text 或其他文字類型
                properties["Rate Limit"] = {
                    "rich_text": [{"text": {"content": model.get_rate_limit()}}]
                }

        return properties

    def update_database(self, models: List[ModelInfo]) -> List[Dict[str, Any]]:
        """批量更新 Notion 資料庫（檢測現有資料並更新或新增）"""
        # 獲取資料庫屬性
        _, available_properties = self.test_connection()

        results = []
        updated_count = 0
        created_count = 0

        for model in models:
            try:
                # 檢查是否已存在
                existing_page = self.find_existing_model(model.name, available_properties)

                if existing_page:
                    # 更新現有記錄
                    result = self.update_existing_page(
                        existing_page["id"],
                        model,
                        available_properties
                    )
                    results.append(result)
                    updated_count += 1
                    print(f"🔄 已更新模型: {model.name}")
                else:
                    # 建立新記錄
                    result = self.create_page(model, available_properties)
                    results.append(result)
                    created_count += 1
                    print(f"✅ 已新增模型: {model.name}")

            except Exception as e:
                print(f"❌ 處理模型失敗 {model.name}: {e}")

        print(f"\n📊 處理結果: 新增 {created_count} 個，更新 {updated_count} 個模型")
        return results