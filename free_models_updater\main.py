"""主要執行模組"""

import asyncio
import os

from free_models_updater.api_client import CohereAPIClient
from free_models_updater.notion_client import NotionClient


async def main():
    """主要執行函數"""
    # 輸入 Notion 資料庫 ID
    database_id = os.getenv("NOTION_DATABASE_ID", "")

    if not database_id:
        database_id = input("請輸入 Notion 資料庫 ID: ").strip()
    
    print("🚀 開始從 Cohere API 獲取模型資料...")
    
    # 初始化客戶端
    api_client = CohereAPIClient()
    notion_client = NotionClient(database_id)

    # 測試 Notion 連接
    print("🔍 測試 Notion API 連接...")
    if not notion_client.test_connection():
        print("❌ Notion 連接失敗，請檢查 API Key 和資料庫 ID")
        return

    try:
        # 獲取模型資料
        models = await api_client.fetch_models()
        print(f"📊 成功獲取 {len(models)} 個模型")
        
        # 顯示模型分類統計
        model_types = {}
        for model in models:
            model_type = model.model_type
            model_types[model_type] = model_types.get(model_type, 0) + 1
        
        print("\n📈 模型分類統計:")
        for model_type, count in model_types.items():
            print(f"  {model_type}: {count} 個")
        
        # 上傳到 Notion
        print("📤 開始上傳到 Notion 資料庫...")
        results = notion_client.update_database(models)
        
        print(f"\n✨ 完成！成功上傳 {len(results)} 個模型到 Notion")
        
    except Exception as e:
        print(f"❌ 執行失敗: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())