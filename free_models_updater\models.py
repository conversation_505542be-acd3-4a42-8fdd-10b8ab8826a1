"""資料模型定義"""

from dataclasses import dataclass


@dataclass
class ModelInfo:
    """模型資訊資料類別"""
    name: str
    endpoints: list[str]
    context_length: int
    supports_vision: bool
    finetuned: bool
    tokenizer_url: str | None = None
    features: str | None = None
    
    @property
    def model_type(self) -> str:
        """根據端點和特性分類模型類型"""
        if "embed" in self.endpoints:
            return "Embedding"
        elif "chat" in self.endpoints or "generate" in self.endpoints:
            return "Text Generation"
        elif "classify" in self.endpoints:
            return "Classification"
        else:
            return "Other"
    
    @property
    def vision_support(self) -> str:
        """視覺支援狀態"""
        return "是" if self.supports_vision else "否"
    
    @property
    def fine_tuned_status(self) -> str:
        """微調狀態"""
        return "是" if self.finetuned else "否"