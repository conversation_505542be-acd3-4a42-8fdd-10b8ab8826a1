"""資料模型定義"""

from dataclasses import dataclass


@dataclass
class ModelInfo:
    """模型資訊資料類別"""
    name: str
    endpoints: list[str]
    context_length: int
    supports_vision: bool
    finetuned: bool
    tokenizer_url: str | None = None
    features: str | None = None
    maximum_output_tokens: int | None = None
    rate_limit: str | None = None
    
    @property
    def model_type(self) -> str:
        """根據端點和特性分類模型類型"""
        if "embed" in self.endpoints:
            return "Embedding"
        elif "chat" in self.endpoints or "generate" in self.endpoints:
            return "Text Generation"
        elif "classify" in self.endpoints:
            return "Classification"
        else:
            return "Other"
    
    @property
    def vision_support(self) -> str:
        """視覺支援狀態"""
        return "是" if self.supports_vision else "否"
    
    @property
    def fine_tuned_status(self) -> str:
        """微調狀態"""
        return "是" if self.finetuned else "否"

    @property
    def vision_status(self) -> str:
        """視覺支援狀態 (適用於新的 Vision 欄位)"""
        return "Yes" if self.supports_vision else "No"

    def get_maximum_output_tokens(self) -> int:
        """獲取最大輸出 tokens，如果 API 沒有提供則使用預設值"""
        if self.maximum_output_tokens is not None:
            return self.maximum_output_tokens

        # 根據模型類型設定預設值
        if "embed" in self.endpoints:
            return 0  # Embedding 模型不產生文字輸出
        elif "chat" in self.endpoints or "generate" in self.endpoints:
            # 根據上下文長度估算輸出長度
            if self.context_length >= 128000:  # 長上下文模型
                return 4096
            elif self.context_length >= 32000:
                return 2048
            else:
                return 1024
        else:
            return 512  # 其他類型的預設值

    def get_rate_limit(self) -> str:
        """獲取速率限制，如果 API 沒有提供則使用預設值"""
        if self.rate_limit is not None:
            return self.rate_limit

        # 根據模型類型設定預設值
        if "embed" in self.endpoints:
            return "1000 requests/minute"
        elif "chat" in self.endpoints or "generate" in self.endpoints:
            return "100 requests/minute"
        else:
            return "500 requests/minute"