"""配置管理模組"""

import os
from dataclasses import dataclass

from dotenv import load_dotenv

load_dotenv()

@dataclass
class Config:
    """應用程式配置"""
    cohere_api_endpoint: str = os.getenv("COHERE_API_ENDPOINT", "")
    cohere_api_key: str = os.getenv("COHERE_API_KEY", "")
    notion_api_key: str = os.getenv("NOTION_API_KEY", "")
    
    def __post_init__(self):  # noqa: D105
        if not self.cohere_api_key:
            raise ValueError("COHERE_API_KEY 未設定")
        if not self.notion_api_key:
            raise ValueError("NOTION_API_KEY 未設定")

config = Config()